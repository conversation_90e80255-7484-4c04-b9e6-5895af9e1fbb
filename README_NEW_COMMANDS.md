# الأوامر الجديدة - مين مشغل وايه شغال

تم إضافة أمرين جديدين لبوت الموسيقى لعرض معلومات التشغيل الحالي.

## الأوامر المضافة

### 1. أمر "مين مشغل"
- **الأمر:** `/مين مشغل`
- **الوظيفة:** يعرض اسم الشخص الذي شغل الأغنية الحالية
- **المثال:**
  ```
  🎵 معلومات المشغل الحالي
  
  👤 المشغل: @username
  🎶 الأغنية: اسم الأغنية
  
  💡 استخدم /ايه شغال لمعرفة تفاصيل أكثر عن الأغنية الحالية.
  ```

### 2. <PERSON><PERSON>ر "ايه شغال"
- **الأمر:** `/ايه شغال` أو `/ايش شغال`
- **الوظيفة:** يعرض تفاصيل كاملة عن الأغنية الحالية
- **المثال:**
  ```
  🎵 الأغنية الحالية
  
  🎶 العنوان: اسم الأغنية
  ⏱️ المدة: 3:45
  ⏯️ تم تشغيل: 1:23
  📊 التقدم: 37.2%
  🎧 النوع: صوت
  👤 شغلها: @username
  
  📋 قائمة الانتظار: 5 أغنية
  
  💡 استخدم /مين مشغل لمعرفة من شغل هذه الأغنية.
  ```

## الملفات المعدلة

### 1. `strings/commands.yml`
تم إضافة الأوامر الجديدة:
```yaml
WHO_PLAYING_COMMAND:
  ar: ["مين مشغل"]
  en: ["whoplaying", "who"]

WHAT_PLAYING_COMMAND:
  ar: ["ايه شغال", "ايش شغال"]
  en: ["whatplaying", "what", "nowplaying"]
```

### 2. `Amusic/plugins/misc/status_commands.py` (ملف جديد)
يحتوي على:
- دالة `who_playing_command()` لأمر "مين مشغل"
- دالة `what_playing_command()` لأمر "ايه شغال"
- معالجة الأخطاء والحالات الخاصة
- تنسيق جميل للرسائل

### 3. `Amusic/plugins/bot/custom_commands.py`
تم إضافة الأوامر الجديدة لقائمة أوامر التشغيل:
```
👤 /مين مشغل - معرفة من شغل الأغنية
🎵 /ايه شغال - تفاصيل الأغنية الحالية
```

## المميزات

### أمر "مين مشغل"
- ✅ يعرض اسم المستخدم الذي شغل الأغنية
- ✅ يعرض عنوان الأغنية الحالية
- ✅ رسالة خطأ واضحة إذا لم يكن هناك تشغيل نشط
- ✅ تصميم جميل ومنظم

### أمر "ايه شغال"
- ✅ يعرض عنوان الأغنية
- ✅ يعرض مدة الأغنية الكاملة
- ✅ يعرض الوقت المشغل حالياً
- ✅ يحسب نسبة التقدم
- ✅ يعرض نوع التشغيل (صوت/فيديو)
- ✅ يعرض اسم المشغل
- ✅ يعرض عدد الأغاني في قائمة الانتظار
- ✅ رسالة خطأ واضحة إذا لم يكن هناك تشغيل نشط

## كيفية الاستخدام

1. تأكد من أن البوت يعمل في المجموعة
2. شغل أي أغنية باستخدام أمر `/تشغيل`
3. استخدم `/مين مشغل` لمعرفة من شغل الأغنية
4. استخدم `/ايه شغال` لمعرفة تفاصيل الأغنية الحالية

## ملاحظات

- الأوامر تعمل فقط في المجموعات (ليس في الرسائل الخاصة)
- إذا لم يكن هناك تشغيل نشط، ستظهر رسالة خطأ واضحة
- الأوامر متوافقة مع النظام الحالي للبوت
- تم إضافة الأوامر لقائمة الأوامر الرسمية في البوت

## الاختبار

تم إنشاء ملف `test_status_commands.py` لاختبار الأوامر الجديدة والتأكد من عملها بشكل صحيح.
