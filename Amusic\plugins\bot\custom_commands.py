



import time
from pyrogram import filters
from pyrogram.types import InlineKeyboardButton, InlineKeyboardMarkup, Message

import config
from strings import command, get_string
from Amusic import app
from Amusic.misc import _boot_
from Amusic.utils.database import get_lang
from Amusic.utils.decorators.language import LanguageStart
from Amusic.utils.formatters import get_readable_time


@app.on_message(command("BOT_STATUS_COMMAND") & filters.group)
@LanguageStart
async def bot_status_command(client, message: Message, _):
    """أمر بوت - للتحقق من حالة البوت"""
    uptime = int(time.time() - _boot_)
    uptime_text = get_readable_time(uptime)
    
    
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("📢 القناة الرسمية", url=config.SUPPORT_CHANNEL if config.SUPPORT_CHANNEL else "https://t.me/your_channel"),
            InlineKeyboardButton("👨‍💻 المطور", url=f"https://t.me/{config.OWNER_ID[0]}" if config.OWNER_ID else "https://t.me/your_developer")
        ]
    ])
    
    await message.reply_text(
        _["bot_status_1"].format(uptime_text),
        reply_markup=keyboard
    )


@app.on_message(command("COMMANDS_COMMAND") & filters.group)
@LanguageStart
async def commands_list(client, message: Message, _):
    """أمر الأوامر - لعرض قائمة أوامر البوت"""
    
    
    keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("🎵 أوامر التشغيل", callback_data="commands_play"),
            InlineKeyboardButton("🔧 أوامر الإدارة", callback_data="commands_admin")
        ],
        [
            InlineKeyboardButton("⚙️ أوامر الإعدادات", callback_data="commands_settings"),
            InlineKeyboardButton("📊 أوامر الإحصائيات", callback_data="commands_stats")
        ],
        [
            InlineKeyboardButton("🎵 قوائم التشغيل", callback_data="commands_playlist"),
            InlineKeyboardButton("🔍 أوامر البحث", callback_data="commands_search")
        ],
        [
            InlineKeyboardButton("📢 القناة", url=config.SUPPORT_CHANNEL if config.SUPPORT_CHANNEL else "https://t.me/your_channel"),
            InlineKeyboardButton("👨‍💻 المطور", url=f"https://t.me/{config.OWNER_ID[0]}" if config.OWNER_ID else "https://t.me/your_developer")
        ],
        [
            InlineKeyboardButton("❌ إغلاق", callback_data="close")
        ]
    ])
    
    await message.reply_text(
        _["commands_1"],
        reply_markup=keyboard
    )


@app.on_callback_query(filters.regex("commands_"))
async def commands_callback(_, query):
    """معالج أزرار قائمة الأوامر"""
    
    language = await get_lang(query.message.chat.id)
    _ = get_string(language)
    
    callback_data = query.data
    
    
    back_keyboard = InlineKeyboardMarkup([
        [
            InlineKeyboardButton("🔙 رجوع", callback_data="commands_back"),
            InlineKeyboardButton("❌ إغلاق", callback_data="close")
        ]
    ])
    
    if callback_data == "commands_play":
        text = """🎵 **أوامر التشغيل** 🎵

🎶 `/تشغيل [اسم الأغنية]` - تشغيل موسيقى
🎥 `/تشغيل -v [اسم الأغنية]` - تشغيل فيديو
⏸️ `/وقف` - إيقاف مؤقت
▶️ `/كمل` - استئناف التشغيل
⏹️ `/ايقاف` - إيقاف التشغيل
⏭️ `/تخطي` - تخطي الأغنية الحالية
🔀 `/خلط` - خلط قائمة التشغيل
🔁 `/تكرار` - تكرار الأغنية
🔇 `/اسكت` - كتم الصوت
🔊 `/اتكلم` - إلغاء كتم الصوت
👤 `/مين مشغل` - معرفة من شغل الأغنية
🎵 `/ايه شغال` - تفاصيل الأغنية الحالية
🎙️ `/مين ف الكول` - عرض المشاركين في المكالمة"""
        
    elif callback_data == "commands_admin":
        text = """🔧 **أوامر الإدارة** 🔧

👑 `/رفع ادمن [المستخدم]` - رفع مشرف
👤 `/تنزيل ادمن [المستخدم]` - تنزيل مشرف
📋 `/الادمنيه` - عرض قائمة المشرفين
🔄 `/ريلود` - إعادة تحميل البوت
🚫 `/حظر [المستخدم]` - حظر مستخدم
✅ `/الغاء حظر [المستخدم]` - إلغاء حظر
📊 `/المحظورين` - عرض المحظورين"""
        
    elif callback_data == "commands_settings":
        text = """⚙️ **أوامر الإعدادات** ⚙️

🔧 `/اعدادات` - إعدادات البوت
🌐 `/لغه` - تغيير اللغة
🎵 `/وضع تشغيل` - وضع التشغيل
📺 `/ربط قناة` - ربط قناة
🔊 `/جودة الصوت` - إعدادات الجودة
🎥 `/جودة الفيديو` - جودة الفيديو
🧹 `/تنظيف` - تنظيف الرسائل"""
        
    elif callback_data == "commands_stats":
        text = """📊 **أوامر الإحصائيات** 📊

📈 `/احصائيات` - إحصائيات البوت
🌍 `/الاحصائيات` - إحصائيات عامة
🏃‍♂️ `/بينج` - سرعة البوت
⚡ `/السرعة` - اختبار السرعة
📱 `/المكالمات النشطة` - المكالمات النشطة
🎥 `/مكالمات الفديو النشطة` - مكالمات الفيديو
👥 `/المطورين` - قائمة المطورين"""
        
    elif callback_data == "commands_playlist":
        text = """🎵 **قوائم التشغيل** 🎵

📋 `/قائمة التشغيل` - عرض قائمتك
➕ `/اضافة لقائمة التشغيل` - إضافة أغنية
🗑️ `/حذف قائمة التشغيل` - حذف أغنية
🎶 `/شغل قائمتي` - تشغيل قائمتك
📊 `/قائمة الانتظار` - قائمة الانتظار
🔝 `/افضل 10` - أفضل الأغاني
🌟 `/تشغيل قائمة` - تشغيل قائمة مخصصة"""
        
    elif callback_data == "commands_search":
        text = """🔍 **أوامر البحث والتحميل** 🔍

🎵 `/تنزيل [اسم الأغنية]` - تحميل أغنية
🎥 `/يوت [رابط]` - تحميل من يوتيوب
📝 `/كلمات [اسم الأغنية]` - كلمات الأغنية
🔍 `/بحث [اسم الأغنية]` - البحث عن أغنية
📻 `/لايف [رابط]` - تشغيل بث مباشر
🎧 `/سبوتيفاي [رابط]` - من سبوتيفاي"""
        
    elif callback_data == "commands_back":
        
        keyboard = InlineKeyboardMarkup([
            [
                InlineKeyboardButton("🎵 أوامر التشغيل", callback_data="commands_play"),
                InlineKeyboardButton("🔧 أوامر الإدارة", callback_data="commands_admin")
            ],
            [
                InlineKeyboardButton("⚙️ أوامر الإعدادات", callback_data="commands_settings"),
                InlineKeyboardButton("📊 أوامر الإحصائيات", callback_data="commands_stats")
            ],
            [
                InlineKeyboardButton("🎵 قوائم التشغيل", callback_data="commands_playlist"),
                InlineKeyboardButton("🔍 أوامر البحث", callback_data="commands_search")
            ],
            [
                InlineKeyboardButton("📢 القناة", url=config.SUPPORT_CHANNEL if config.SUPPORT_CHANNEL else "https://t.me/your_channel"),
                InlineKeyboardButton("👨‍💻 المطور", url=f"https://t.me/{config.OWNER_ID[0]}" if config.OWNER_ID else "https://t.me/your_developer")
            ],
            [
                InlineKeyboardButton("❌ إغلاق", callback_data="close")
            ]
        ])
        
        await query.edit_message_text(
            _["commands_1"],
            reply_markup=keyboard
        )
        return
    
    
    await query.edit_message_text(
        text,
        reply_markup=back_keyboard
    )

    await query.answer()



