#!/usr/bin/env python3
"""
اختبار بسيط للأوامر الجديدة
"""

import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """اختبار استيراد الوحدات"""
    try:
        from strings import command, get_string
        print("✅ تم استيراد strings بنجاح")
        
        from Amusic.utils.database import get_lang, get_active_chats, is_music_playing
        print("✅ تم استيراد database utils بنجاح")
        
        from Amusic.utils.decorators.language import LanguageStart
        print("✅ تم استيراد language decorators بنجاح")
        
        from Amusic.utils.formatters import seconds_to_min
        print("✅ تم استيراد formatters بنجاح")
        
        return True
    except ImportError as e:
        print(f"❌ خطأ في الاستيراد: {e}")
        return False

def test_commands():
    """اختبار الأوامر الجديدة"""
    try:
        from strings import get_command
        
        # اختبار أمر "مين مشغل"
        who_commands = get_command("WHO_PLAYING_COMMAND")
        print(f"✅ أمر 'مين مشغل' متاح: {who_commands}")
        
        # اختبار أمر "ايه شغال"
        what_commands = get_command("WHAT_PLAYING_COMMAND")
        print(f"✅ أمر 'ايه شغال' متاح: {what_commands}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في اختبار الأوامر: {e}")
        return False

if __name__ == "__main__":
    print("🧪 بدء اختبار الأوامر الجديدة...")
    print("=" * 50)
    
    # اختبار الاستيرادات
    print("\n📦 اختبار الاستيرادات:")
    imports_ok = test_imports()
    
    # اختبار الأوامر
    print("\n🎵 اختبار الأوامر:")
    commands_ok = test_commands()
    
    print("\n" + "=" * 50)
    if imports_ok and commands_ok:
        print("🎉 جميع الاختبارات نجحت! الأوامر جاهزة للاستخدام.")
        print("\n📋 الأوامر المتاحة:")
        print("   • /مين مشغل - لمعرفة من شغل الأغنية الحالية")
        print("   • /ايه شغال - لمعرفة تفاصيل الأغنية الحالية")
    else:
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
