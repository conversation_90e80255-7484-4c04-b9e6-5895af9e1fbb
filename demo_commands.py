#!/usr/bin/env python3
"""
عرض توضيحي للأوامر الجديدة
"""

def demo_who_playing():
    """عرض توضيحي لأمر مين مشغل"""
    print("🎵 **معلومات المشغل الحالي**")
    print()
    print("👤 **المشغل:** @ahmed_music")
    print("🎶 **الأغنية:** أغنية جميلة")
    print()
    print("💡 استخدم `/ايه شغال` لمعرفة تفاصيل أكثر عن الأغنية الحالية.")

def demo_what_playing():
    """عرض توضيحي لأمر ايه شغال"""
    print("🎵 **الأغنية الحالية**")
    print()
    print("🎶 **العنوان:** أغنية جميلة")
    print("⏱️ **المدة:** 3:45")
    print("⏯️ **تم تشغيل:** 1:23")
    print("📊 **التقدم:** 37.2%")
    print("🎧 **النوع:** صوت")
    print("👤 **شغلها:** @ahmed_music")
    print()
    print("📋 **قائمة الانتظار:** 5 أغنية")
    print()
    print("💡 استخدم `/مين مشغل` لمعرفة من شغل هذه الأغنية.")

def demo_no_music():
    """عرض توضيحي عندما لا يوجد تشغيل"""
    print("❌ لا يوجد تشغيل نشط حالياً في هذه المجموعة.")

if __name__ == "__main__":
    print("🎵 عرض توضيحي للأوامر الجديدة")
    print("=" * 50)
    
    print("\n1️⃣ أمر: /مين مشغل")
    print("-" * 30)
    demo_who_playing()
    
    print("\n2️⃣ أمر: /ايه شغال")
    print("-" * 30)
    demo_what_playing()
    
    print("\n3️⃣ عندما لا يوجد تشغيل:")
    print("-" * 30)
    demo_no_music()
    
    print("\n" + "=" * 50)
    print("🎉 الأوامر جاهزة للاستخدام في البوت!")
    print("\n📋 كيفية الاستخدام:")
    print("   1. شغل البوت في مجموعة")
    print("   2. شغل أي أغنية باستخدام /تشغيل")
    print("   3. استخدم /مين مشغل أو /ايه شغال")
