#!/usr/bin/env python3
"""
اختبار أمر "مين ف الكول"
"""

def demo_who_in_call_with_participants():
    """عرض توضيحي لأمر مين ف الكول مع وجود مشاركين"""
    print("🎙️ **المشاركون في المكالمة الصوتية** (4)")
    print()
    print("👤 **أحمد محمد** @ahmed_music 🎤")
    print("👤 **سارة علي** @sara_voice 🔇")
    print("👤 **محمد حسن** @mohamed_h 📹")
    print("👤 **فاطمة أحمد** @fatima_a 🎤 ✋")
    print()
    print("📊 **إجمالي المشاركين:** 4")
    print()
    print("🔇 = مكتوم | 📹 = فيديو | 🖥️ = مشاركة الشاشة | ✋ = رفع اليد")

def demo_who_in_call_empty():
    """عرض توضيحي لأمر مين ف الكول بدون مشاركين"""
    print("🚫 لا يوجد مشاركون في المكالمة الصوتية حالياً.")

def demo_who_in_call_no_active():
    """عرض توضيحي عندما لا توجد مكالمة نشطة"""
    print("❌ لا توجد مكالمة صوتية نشطة حالياً في هذه المجموعة.")

def demo_who_in_call_loading():
    """عرض توضيحي لرسالة التحميل"""
    print("🔄 جارٍ جلب قائمة المشاركين في المكالمة...")

if __name__ == "__main__":
    print("🎙️ عرض توضيحي لأمر 'مين ف الكول'")
    print("=" * 50)
    
    print("\n1️⃣ رسالة التحميل:")
    print("-" * 30)
    demo_who_in_call_loading()
    
    print("\n2️⃣ مع وجود مشاركين:")
    print("-" * 30)
    demo_who_in_call_with_participants()
    
    print("\n3️⃣ بدون مشاركين:")
    print("-" * 30)
    demo_who_in_call_empty()
    
    print("\n4️⃣ لا توجد مكالمة نشطة:")
    print("-" * 30)
    demo_who_in_call_no_active()
    
    print("\n" + "=" * 50)
    print("🎉 أمر 'مين ف الكول' جاهز للاستخدام!")
    print("\n📋 كيفية الاستخدام:")
    print("   1. تأكد من وجود مكالمة صوتية نشطة")
    print("   2. استخدم الأمر /مين ف الكول")
    print("   3. انتظر جلب قائمة المشاركين")
    print("\n🔧 الميزات:")
    print("   • عرض أسماء المشاركين")
    print("   • عرض معرفات المستخدمين")
    print("   • عرض حالة كل مشارك (مكتوم، فيديو، إلخ)")
    print("   • عدد إجمالي المشاركين")
    print("   • رموز توضيحية للحالات المختلفة")
