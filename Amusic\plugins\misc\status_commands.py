#
# أوامر حالة التشغيل - مين مشغل وايه شغال
#

from pyrogram import filters
from pyrogram.types import Message

from strings import command, get_string
from Amusic import app
from Amusic.misc import db
from Amusic.utils.database import get_lang, get_active_chats, is_music_playing, get_assistant
from Amusic.utils.decorators.language import LanguageStart
from Amusic.utils.formatters import seconds_to_min


@app.on_message(command("WHO_PLAYING_COMMAND") & filters.group)
@LanguageStart
async def who_playing_command(client, message: Message, _):
    """أمر مين مشغل - لعرض الشخص الذي شغل الأغنية الحالية"""
    
    chat_id = message.chat.id
    
    # التحقق من وجود تشغيل نشط
    if not await is_music_playing(chat_id):
        return await message.reply_text("❌ لا يوجد تشغيل نشط حالياً في هذه المجموعة.")
    
    # الحصول على معلومات التشغيل الحالي
    playing = db.get(chat_id)
    if not playing:
        return await message.reply_text("❌ لا يوجد تشغيل نشط حالياً.")
    
    current_track = playing[0]
    user_who_played = current_track.get("by", "غير معروف")
    title = current_track.get("title", "غير معروف")
    
    response_text = f"""🎵 **معلومات المشغل الحالي**

👤 **المشغل:** {user_who_played}
🎶 **الأغنية:** {title}

💡 استخدم `/ايه شغال` لمعرفة تفاصيل أكثر عن الأغنية الحالية."""
    
    await message.reply_text(response_text)


@app.on_message(command("WHAT_PLAYING_COMMAND") & filters.group)
@LanguageStart
async def what_playing_command(client, message: Message, _):
    """أمر ايه شغال - لعرض تفاصيل الأغنية الحالية"""
    
    chat_id = message.chat.id
    
    # التحقق من وجود تشغيل نشط
    if not await is_music_playing(chat_id):
        return await message.reply_text("❌ لا يوجد تشغيل نشط حالياً في هذه المجموعة.")
    
    # الحصول على معلومات التشغيل الحالي
    playing = db.get(chat_id)
    if not playing:
        return await message.reply_text("❌ لا يوجد تشغيل نشط حالياً.")
    
    current_track = playing[0]
    
    # استخراج المعلومات
    title = current_track.get("title", "غير معروف")
    duration = current_track.get("dur", "غير معروف")
    user_who_played = current_track.get("by", "غير معروف")
    stream_type = current_track.get("streamtype", "غير معروف")
    played_time = current_track.get("played", 0)
    total_seconds = current_track.get("seconds", 0)
    
    # تحويل الوقت المشغل إلى دقائق وثواني
    if played_time and total_seconds:
        played_duration = seconds_to_min(played_time)
        progress_percentage = round((played_time / total_seconds) * 100, 1) if total_seconds > 0 else 0
    else:
        played_duration = "غير معروف"
        progress_percentage = 0
    
    # تحديد نوع التشغيل
    stream_type_ar = "صوت" if stream_type == "audio" else "فيديو" if stream_type == "video" else stream_type
    
    # عدد الأغاني في قائمة الانتظار
    queue_count = len(playing) - 1 if len(playing) > 1 else 0
    
    response_text = f"""🎵 **الأغنية الحالية**

🎶 **العنوان:** {title}
⏱️ **المدة:** {duration}
⏯️ **تم تشغيل:** {played_duration}
📊 **التقدم:** {progress_percentage}%
🎧 **النوع:** {stream_type_ar}
👤 **شغلها:** {user_who_played}

📋 **قائمة الانتظار:** {queue_count} أغنية

💡 استخدم `/مين مشغل` لمعرفة من شغل هذه الأغنية."""

    await message.reply_text(response_text)


@app.on_message(command("WHO_IN_CALL_COMMAND") & filters.group)
@LanguageStart
async def who_in_call_command(client, message: Message, _):
    """أمر مين ف الكول - لعرض قائمة المشاركين في المكالمة الصوتية"""

    chat_id = message.chat.id

    # التحقق من وجود مكالمة نشطة
    if not await is_music_playing(chat_id):
        return await message.reply_text("❌ لا توجد مكالمة صوتية نشطة حالياً في هذه المجموعة.")

    # إرسال رسالة انتظار
    loading_msg = await message.reply_text("🔄 جارٍ جلب قائمة المشاركين في المكالمة...")

    try:
        # الحصول على المساعد المسؤول عن هذه المجموعة
        assistant = await get_assistant(chat_id)
        if not assistant:
            return await loading_msg.edit_text("❌ لا يمكن العثور على المساعد المسؤول عن هذه المجموعة.")

        # الحصول على قائمة المشاركين في المكالمة
        participants = []
        participant_count = 0

        try:
            async for member in assistant.get_call_members(chat_id):
                if member.chat:
                    participant_count += 1
                    # الحصول على معلومات المستخدم
                    try:
                        user_info = await app.get_chat(member.chat.id)
                        name = user_info.first_name or "غير معروف"
                        if user_info.last_name:
                            name += f" {user_info.last_name}"
                        username = f"@{user_info.username}" if user_info.username else "لا يوجد معرف"

                        # تحديد حالة المشارك
                        status_icons = []
                        if hasattr(member, 'muted') and member.muted:
                            status_icons.append("🔇")
                        if hasattr(member, 'video') and member.video:
                            status_icons.append("📹")
                        if hasattr(member, 'screen_sharing') and member.screen_sharing:
                            status_icons.append("🖥️")
                        if hasattr(member, 'hand_raised') and member.hand_raised:
                            status_icons.append("✋")

                        status = " ".join(status_icons) if status_icons else "🎤"

                        participants.append(f"👤 **{name}** {username} {status}")

                    except Exception as e:
                        # في حالة عدم القدرة على الحصول على معلومات المستخدم
                        participants.append(f"👤 **مستخدم** (ID: {member.chat.id}) 🎤")

        except Exception as e:
            return await loading_msg.edit_text(f"❌ حدث خطأ أثناء جلب قائمة المشاركين: {str(e)}")

        # تكوين الرسالة النهائية
        if participant_count == 0:
            response_text = "🚫 لا يوجد مشاركون في المكالمة الصوتية حالياً."
        else:
            response_text = f"🎙️ **المشاركون في المكالمة الصوتية** ({participant_count})\n\n"

            if participants:
                response_text += "\n".join(participants)
            else:
                response_text += "👤 المساعد فقط في المكالمة"

            response_text += f"\n\n📊 **إجمالي المشاركين:** {participant_count}"
            response_text += "\n\n🔇 = مكتوم | 📹 = فيديو | 🖥️ = مشاركة الشاشة | ✋ = رفع اليد"

        await loading_msg.edit_text(response_text)

    except Exception as e:
        await loading_msg.edit_text(f"❌ حدث خطأ غير متوقع: {str(e)}")
